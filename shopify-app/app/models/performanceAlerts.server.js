import { shopDetails } from '../helper/shopDetails';
import { loggerInfo, loggerError } from '../helper/loggerHelper';
import { supabase } from '../db/supabase_insert_helper';
import { fetchCollectionAnalytics, getInventoryAlerts } from './analytics.server';
import cache, { getPerformanceAlertsCacheKey } from '../utils/cache.server';

export const generatePerformanceAlerts = async (admin) => {
  const shop = await shopDetails(admin);
  const storeName = shop?.data?.shop?.name;
  const shopDomain = shop?.data?.shop?.myshopifyDomain;

  // Check cache first
  const cacheKey = getPerformanceAlertsCacheKey(shopDomain);
  const cachedResult = cache.get(cacheKey);
  if (cachedResult) {
    loggerInfo("Returning cached performance alerts", storeName);
    return cachedResult;
  }

  loggerInfo("Generating performance alerts", storeName);

  try {
    const alerts = [];

    // Get alert settings for the shop
    const alertSettings = await getAlertSettings(shopDomain);

    // Check collection performance
    const performanceAlerts = await checkCollectionPerformance(admin, alertSettings);
    alerts.push(...performanceAlerts);

    // Check inventory issues
    const inventoryAlerts = await checkInventoryIssues(admin, alertSettings);
    alerts.push(...inventoryAlerts);

    // Check for optimization opportunities
    const optimizationAlerts = await checkOptimizationOpportunities(admin);
    alerts.push(...optimizationAlerts);



    loggerInfo(
      "Performance alerts generated successfully",
      storeName,
      { alertCount: alerts.length }
    );

    const result = {
      status: 200,
      message: "Alerts generated successfully",
      data: alerts
    };

    // Cache the result for 3 minutes
    cache.set(cacheKey, result, 3 * 60 * 1000);

    return result;

  } catch (error) {
    loggerError(
      "Error generating performance alerts",
      storeName,
      error.message
    );

    return {
      status: 500,
      message: "Failed to generate alerts",
      data: []
    };
  }
};

async function checkCollectionPerformance(admin, settings) {
  const alerts = [];

  try {
    let collections = [];

    // First try to get collections from database
    try {
      const shop = await shopDetails(admin);
      const storeName = shop?.data?.shop?.name;

      const { data: storeData, error: storeError } = await supabase
        .from('stores')
        .select('id')
        .eq('store_name', storeName)
        .single();

      if (!storeError && storeData) {
        const { data: dbCollections, error: collectionsError } = await supabase
          .from('collections')
          .select('*')
          .eq('store_id', storeData.id)
          .eq('status', 'published')
          .limit(10);

        if (!collectionsError && dbCollections && dbCollections.length > 0) {
          collections = dbCollections.map(col => ({
            id: col.id,
            collection_name: col.collection_name,
            source: 'database'
          }));
        }
      }
    } catch (dbError) {
      loggerError("Error fetching from database, will try Shopify API", null, dbError.message);
    }

    // If no collections from database, fetch from Shopify API
    if (collections.length === 0) {
      try {
        const collectionsQuery = `
          query {
            collections(first: 10) {
              nodes {
                id
                title
                productsCount
              }
            }
          }
        `;

        const response = await admin.graphql(collectionsQuery);
        const data = await response.json();

        if (data?.data?.collections?.nodes) {
          collections = data.data.collections.nodes.map(col => ({
            id: col.id,
            collection_name: col.title,
            productsCount: col.productsCount,
            source: 'shopify'
          }));
        }
      } catch (apiError) {
        loggerError("Error fetching from Shopify API", null, apiError.message);
      }
    }

    // Generate alerts for collections using real analytics
    for (const collection of collections) {
      try {
        // Fetch real analytics for this collection
        const analyticsResult = await fetchCollectionAnalytics(admin, collection.id, '30d');

        if (analyticsResult.status === 200 && analyticsResult.data) {
          const analytics = analyticsResult.data.analytics;
          const collectionData = analyticsResult.data.collection;

          const performanceScore = analytics.performanceScore;
          const revenue = analytics.totalRevenue;
          const conversionRate = analytics.conversionRate;
          const estimatedViews = analytics.estimatedViews;
          const orders = analytics.collectionOrders;

          // Check for low performance
          if (performanceScore < settings.lowPerformanceThreshold) {
            alerts.push({
              id: `perf_low_${collection.id}`,
              type: 'critical',
              title: 'Low Collection Performance',
              description: `Collection "${collectionData.title}" is underperforming with a ${performanceScore}% performance score. Consider optimizing product selection and pricing.`,
              collectionName: collectionData.title,
              collectionId: collection.id,
              timestamp: new Date().toLocaleString(),
              metrics: {
                'Performance Score': `${performanceScore}%`,
                'Views (Est.)': estimatedViews.toLocaleString(),
                'Conversion Rate': `${conversionRate.toFixed(2)}%`,
                'Revenue (30d)': `$${revenue.toFixed(2)}`,
                'Orders': orders.toString()
              },
              actionLabel: 'Optimize Collection',
              actionType: 'optimize'
            });
          }

          // Check for high performance (opportunity to replicate)
          if (performanceScore > settings.highPerformanceThreshold) {
            alerts.push({
              id: `perf_high_${collection.id}`,
              type: 'success',
              title: 'High-Performing Collection',
              description: `Collection "${collectionData.title}" is performing exceptionally well with a ${performanceScore}% performance score! Consider creating similar collections.`,
              collectionName: collectionData.title,
              collectionId: collection.id,
              timestamp: new Date().toLocaleString(),
              metrics: {
                'Performance Score': `${performanceScore}%`,
                'Views (Est.)': estimatedViews.toLocaleString(),
                'Conversion Rate': `${conversionRate.toFixed(2)}%`,
                'Revenue (30d)': `$${revenue.toFixed(2)}`,
                'Orders': orders.toString()
              },
              actionLabel: 'Create Similar',
              actionType: 'replicate'
            });
          }

          // Check for collections with no recent sales
          if (orders === 0 && revenue === 0) {
            alerts.push({
              id: `perf_no_sales_${collection.id}`,
              type: 'warning',
              title: 'Collection with No Recent Sales',
              description: `Collection "${collectionData.title}" has had no sales in the last 30 days. Consider reviewing product selection or marketing strategy.`,
              collectionName: collectionData.title,
              collectionId: collection.id,
              timestamp: new Date().toLocaleString(),
              metrics: {
                'Performance Score': `${performanceScore}%`,
                'Revenue (30d)': '$0.00',
                'Orders': '0',
                'Products': collectionData.productsCount.toString()
              },
              actionLabel: 'Review Collection',
              actionType: 'optimize'
            });
          }
        }
      } catch (error) {
        loggerError("Error analyzing collection performance", null, error.message);
        // Continue with next collection if one fails
      }
    }
  } catch (error) {
    loggerError("Error checking collection performance", null, error.message);
  }

  return alerts;
}

async function checkInventoryIssues(admin, settings) {
  const alerts = [];

  if (!settings.outOfStockAlerts) return alerts;

  try {
    // Use the analytics service to get inventory data
    const inventoryResult = await getInventoryAlerts(admin, settings.lowInventoryThreshold);

    if (inventoryResult.status === 200 && inventoryResult.data) {
      for (const product of inventoryResult.data) {
        if (product.collections.nodes.length > 0) {
          const inventory = product.totalInventory;
          const isOutOfStock = inventory === 0;

          // Calculate estimated lost sales for out of stock items
          let lostSalesEstimate = 0;
          if (isOutOfStock && product.variants.nodes.length > 0) {
            const avgPrice = product.variants.nodes.reduce((sum, variant) =>
              sum + parseFloat(variant.price), 0) / product.variants.nodes.length;
            lostSalesEstimate = avgPrice * 5; // Estimate 5 lost sales per day
          }

          alerts.push({
            id: `inventory_${product.id}`,
            type: isOutOfStock ? 'critical' : 'warning',
            title: isOutOfStock ? 'Product Out of Stock' : 'Low Inventory Alert',
            description: isOutOfStock
              ? `Product "${product.title}" is completely out of stock and appears in ${product.collections.nodes.length} collection(s). This may be causing lost sales.`
              : `Product "${product.title}" has only ${inventory} units remaining and appears in ${product.collections.nodes.length} collection(s). Consider restocking soon.`,
            collectionName: product.collections.nodes[0].title,
            collectionId: product.collections.nodes[0].id,
            timestamp: new Date().toLocaleString(),
            metrics: {
              'Current Inventory': inventory.toString(),
              'Collections': product.collections.nodes.length.toString(),
              'Threshold': settings.lowInventoryThreshold.toString(),
              ...(isOutOfStock && lostSalesEstimate > 0 && {
                'Est. Lost Sales/Day': `$${lostSalesEstimate.toFixed(2)}`
              })
            },
            actionLabel: isOutOfStock ? 'Restock Now' : 'Manage Inventory',
            actionType: 'inventory'
          });
        }
      }
    }
  } catch (error) {
    loggerError("Error checking inventory issues", null, error.message);
  }

  return alerts;
}

async function checkOptimizationOpportunities(admin) {
  const alerts = [];

  try {
    // Get store data first
    const shop = await shopDetails(admin);
    const storeName = shop?.data?.shop?.name;

    const { data: storeData, error: storeError } = await supabase
      .from('stores')
      .select('id')
      .eq('store_name', storeName)
      .single();

    if (storeError || !storeData) {
      throw new Error('Store not found');
    }

    // Check for collections that haven't been updated recently
    const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString();

    const { data: staleCollections, error: collectionsError } = await supabase
      .from('collections')
      .select('*')
      .eq('store_id', storeData.id)
      .eq('status', 'published')
      .lt('generated_time', thirtyDaysAgo)
      .limit(5);

    if (collectionsError) {
      throw new Error(collectionsError.message);
    }

    for (const collection of staleCollections || []) {
      alerts.push({
        id: `stale_${collection.id}`,
        type: 'warning',
        title: 'Collection Needs Refresh',
        description: `Collection "${collection.collection_name}" hasn't been updated in over 30 days. Consider refreshing with new products.`,
        collectionName: collection.collection_name,
        collectionId: collection.id,
        timestamp: new Date().toLocaleString(),
        metrics: {
          'Last Updated': new Date(collection.generated_time).toLocaleDateString(),
          'Days Ago': Math.floor((Date.now() - new Date(collection.generated_time)) / (1000 * 60 * 60 * 24))
        },
        actionLabel: 'Refresh Collection',
        actionType: 'refresh'
      });
    }
  } catch (error) {
    loggerError("Error checking optimization opportunities", null, error.message);
  }

  return alerts;
}



export const getAlertSettings = async (shopDomain) => {
  try {
    // Try to get existing settings from database
    const { data: settings, error } = await supabase
      .from('alertsettings')
      .select('*')
      .eq('shop_domain', shopDomain)
      .single();

    if (error && error.code !== 'PGRST116') { // PGRST116 is "not found" error
      throw error;
    }

    if (settings) {
      return {
        shopDomain: settings.shop_domain,
        lowPerformanceThreshold: settings.low_performance_threshold,
        highPerformanceThreshold: settings.high_performance_threshold,
        outOfStockAlerts: settings.out_of_stock_alerts,
        lowInventoryThreshold: settings.low_inventory_threshold,
        frequency: settings.frequency,
        emailNotifications: settings.email_notifications
      };
    }

    // Return default settings if none exist
    return {
      shopDomain,
      lowPerformanceThreshold: 30,
      highPerformanceThreshold: 80,
      outOfStockAlerts: true,
      lowInventoryThreshold: 10,
      frequency: 'daily',
      emailNotifications: true
    };
  } catch (error) {
    loggerError("Error getting alert settings", shopDomain, error.message);

    // Return default settings on error
    return {
      shopDomain,
      lowPerformanceThreshold: 30,
      highPerformanceThreshold: 80,
      outOfStockAlerts: true,
      lowInventoryThreshold: 10,
      frequency: 'daily',
      emailNotifications: true
    };
  }
};

export const updateAlertSettings = async (shopDomain, newSettings) => {
  try {
    const settingsData = {
      shop_domain: shopDomain,
      low_performance_threshold: newSettings.lowPerformanceThreshold,
      high_performance_threshold: newSettings.highPerformanceThreshold,
      out_of_stock_alerts: newSettings.outOfStockAlerts,
      low_inventory_threshold: newSettings.lowInventoryThreshold,
      frequency: newSettings.frequency,
      email_notifications: newSettings.emailNotifications,
      updated_at: new Date().toISOString()
    };

    const { data: settings, error } = await supabase
      .from('alertsettings')
      .upsert(settingsData, {
        onConflict: 'shop_domain',
        returning: 'representation'
      })
      .select()
      .single();

    if (error) {
      throw error;
    }

    loggerInfo("Alert settings updated successfully", shopDomain);

    return {
      status: 200,
      message: "Settings updated successfully",
      data: {
        shopDomain: settings.shop_domain,
        lowPerformanceThreshold: settings.low_performance_threshold,
        highPerformanceThreshold: settings.high_performance_threshold,
        outOfStockAlerts: settings.out_of_stock_alerts,
        lowInventoryThreshold: settings.low_inventory_threshold,
        frequency: settings.frequency,
        emailNotifications: settings.email_notifications
      }
    };
  } catch (error) {
    loggerError("Error updating alert settings", shopDomain, error.message);

    return {
      status: 500,
      message: "Failed to update settings",
      data: null
    };
  }
};

export const dismissAlert = async (shopDomain, alertId) => {
  try {
    // Store dismissed alerts to avoid showing them again
    const { error } = await supabase
      .from('dismissedalerts')
      .insert({
        shop_domain: shopDomain,
        alert_id: alertId,
        dismissed_at: new Date().toISOString()
      });

    if (error) {
      throw error;
    }

    loggerInfo("Alert dismissed successfully", shopDomain, { alertId });

    return {
      status: 200,
      message: "Alert dismissed successfully"
    };
  } catch (error) {
    loggerError("Error dismissing alert", shopDomain, error.message);

    return {
      status: 500,
      message: "Failed to dismiss alert"
    };
  }
};
