import { shopDetails } from '../helper/shopDetails';
import { loggerInfo, loggerError } from '../helper/loggerHelper';
import { supabase } from '../db/supabase_insert_helper';

export const getInventoryData = async (admin) => {
  const shop = await shopDetails(admin);
  const storeName = shop?.data?.shop?.name;

  loggerInfo("Fetching inventory data for collections", storeName);

  try {
    // Get all products with their inventory and collection associations
    const productsQuery = `
      query {
        products(first: 100) {
          nodes {
            id
            title
            handle
            featuredImage {
              url
            }
            totalInventory
            variants(first: 1) {
              nodes {
                inventoryQuantity
                price
              }
            }
            collections(first: 10) {
              nodes {
                id
                title
              }
            }
          }
        }
      }
    `;

    const response = await admin.graphql(productsQuery);
    const data = await response.json();

    if (!data?.data?.products?.nodes) {
      throw new Error("No products found");
    }

    const products = data.data.products.nodes.map(product => ({
      id: product.id,
      title: product.title,
      handle: product.handle,
      image: product.featuredImage?.url,
      inventory: product.totalInventory || 0,
      price: product.variants.nodes[0]?.price || '0.00',
      collections: product.collections.nodes.map(c => c.title),
      collectionIds: product.collections.nodes.map(c => c.id),
      isVisible: product.totalInventory > 0 // Default visibility based on inventory
    }));

    loggerInfo(
      "Inventory data fetched successfully",
      storeName,
      { productCount: products.length }
    );

    return {
      status: 200,
      message: "Inventory data fetched successfully",
      data: products
    };

  } catch (error) {
    loggerError(
      "Error fetching inventory data",
      storeName,
      error.message
    );

    return {
      status: 500,
      message: "Failed to fetch inventory data",
      data: []
    };
  }
};

export const updateProductVisibility = async (admin, productId, isVisible) => {
  const shop = await shopDetails(admin);
  const storeName = shop?.data?.shop?.name;

  loggerInfo(
    "Updating product visibility",
    storeName,
    { productId, isVisible }
  );

  try {
    // In Shopify, we can't directly hide products from collections,
    // but we can update the product's status or use metafields
    // For this implementation, we'll use a metafield to track visibility

    const metafieldMutation = `
      mutation metafieldsSet($metafields: [MetafieldsSetInput!]!) {
        metafieldsSet(metafields: $metafields) {
          metafields {
            id
            key
            value
          }
          userErrors {
            field
            message
          }
        }
      }
    `;

    const variables = {
      metafields: [
        {
          ownerId: productId,
          namespace: "rank_collections",
          key: "collection_visibility",
          value: isVisible.toString(),
          type: "boolean"
        }
      ]
    };

    const response = await admin.graphql(metafieldMutation, { variables });
    const result = await response.json();

    if (result.data?.metafieldsSet?.userErrors?.length > 0) {
      throw new Error(result.data.metafieldsSet.userErrors[0].message);
    }

    loggerInfo(
      "Product visibility updated successfully",
      storeName,
      { productId, isVisible }
    );

    return {
      status: 200,
      message: "Product visibility updated successfully",
      data: { productId, isVisible }
    };

  } catch (error) {
    loggerError(
      "Error updating product visibility",
      storeName,
      error.message,
      { productId }
    );

    return {
      status: 500,
      message: "Failed to update product visibility",
      data: null
    };
  }
};

export const bulkInventoryAction = async (admin, action, productIds = []) => {
  const shop = await shopDetails(admin);
  const storeName = shop?.data?.shop?.name;

  loggerInfo(
    "Performing bulk inventory action",
    storeName,
    { action, productCount: productIds.length }
  );
  console.log('bulkInventoryAction called:', { action, productIds, storeName });

  try {
    let results = [];

    switch (action) {
      case 'hide':
        console.log('Processing hide action for products:', productIds);
        for (const productId of productIds) {
          const result = await updateProductVisibility(admin, productId, false);
          results.push(result);
        }
        break;

      case 'show':
        console.log('Processing show action for products:', productIds);
        for (const productId of productIds) {
          const result = await updateProductVisibility(admin, productId, true);
          results.push(result);
        }
        break;

      case 'auto-optimize':
        console.log('Processing auto-optimize action');
        results = await autoOptimizeInventory(admin);
        console.log('Auto-optimize results:', results);
        break;

      default:
        throw new Error(`Unknown action: ${action}`);
    }

    const successCount = results.filter(r => r.status === 200).length;

    loggerInfo(
      "Bulk inventory action completed",
      storeName,
      { action, successCount, totalCount: results.length }
    );

    return {
      status: 200,
      message: `Bulk action completed: ${successCount}/${results.length} successful`,
      data: { successCount, totalCount: results.length, results }
    };

  } catch (error) {
    loggerError(
      "Error performing bulk inventory action",
      storeName,
      error.message,
      { action }
    );

    return {
      status: 500,
      message: "Failed to perform bulk action",
      data: null
    };
  }
};

async function autoOptimizeInventory(admin) {
  const shop = await shopDetails(admin);
  const storeName = shop?.data?.shop?.name;
  const shopDomain = shop?.data?.shop?.myshopifyDomain;

  loggerInfo("Starting intelligent auto-optimization of inventory", storeName);

  try {
    const results = [];

    // Get inventory settings with fallback defaults
    let settings;
    try {
      settings = await getInventorySettings(shopDomain);
    } catch (error) {
      loggerInfo("Using default inventory settings", storeName);
      settings = {
        autoHideOutOfStock: true,
        promoteHighInventory: true,
        lowInventoryThreshold: 10,
        highInventoryThreshold: 100
      };
    }

    loggerInfo("Auto-optimize settings", storeName, settings);

    // Get current inventory data
    const inventoryData = await getInventoryData(admin);

    if (inventoryData.status !== 200) {
      throw new Error(`Failed to fetch inventory data: ${inventoryData.message}`);
    }

    const products = inventoryData.data;
    loggerInfo("Processing products for auto-optimization", storeName, { productCount: products.length });



    // Step 1: Auto-hide out-of-stock products if enabled
    if (settings.autoHideOutOfStock) {
      const outOfStockProducts = products.filter(p => p.inventory === 0);
      loggerInfo(`Found ${outOfStockProducts.length} out-of-stock products to hide`, storeName);

      for (const product of outOfStockProducts) {
        try {
          const result = await updateProductVisibility(admin, product.id, false);
          results.push({
            ...result,
            action: 'hide_out_of_stock',
            productTitle: product.title,
            reason: 'Product is out of stock'
          });
          loggerInfo(`Hidden out-of-stock product: ${product.title}`, storeName);
        } catch (error) {
          loggerError(`Failed to hide product ${product.title}`, storeName, error.message);
          results.push({
            status: 500,
            action: 'hide_out_of_stock',
            productTitle: product.title,
            reason: 'Failed to hide product',
            error: error.message
          });
        }
      }
    }

    // Step 2: Promote high-inventory products if enabled
    if (settings.promoteHighInventory) {
      const highInventoryProducts = products.filter(
        p => p.inventory >= settings.highInventoryThreshold && p.inventory > 0
      );
      loggerInfo(`Found ${highInventoryProducts.length} high-inventory products to promote`, storeName);

      for (const product of highInventoryProducts) {
        try {
          // Ensure high-inventory products are visible
          const result = await updateProductVisibility(admin, product.id, true);
          results.push({
            ...result,
            action: 'promote_high_inventory',
            productTitle: product.title,
            reason: `High inventory: ${product.inventory} units`
          });
          loggerInfo(`Promoted high-inventory product: ${product.title} (${product.inventory} units)`, storeName);
        } catch (error) {
          loggerError(`Failed to promote product ${product.title}`, storeName, error.message);
          results.push({
            status: 500,
            action: 'promote_high_inventory',
            productTitle: product.title,
            reason: 'Failed to promote product',
            error: error.message
          });
        }
      }
    }

    // Step 3: Optimize collection ordering based on inventory levels
    try {
      loggerInfo("Starting collection optimization", storeName);
      const optimizationResults = await optimizeCollectionsByInventory(admin, products, settings);
      results.push(...optimizationResults);
      loggerInfo(`Collection optimization completed: ${optimizationResults.length} collections processed`, storeName);
    } catch (error) {
      loggerError("Failed to optimize collections", storeName, error.message);
      results.push({
        status: 500,
        action: 'optimize_collection',
        reason: 'Failed to optimize collections',
        error: error.message
      });
    }

    // Step 4: Generate inventory-based recommendations
    try {
      const recommendations = await generateInventoryOptimizationRecommendations(products, settings);
      loggerInfo(`Generated ${recommendations.length} optimization recommendations`, storeName);
    } catch (error) {
      loggerError("Failed to generate recommendations", storeName, error.message);
    }

    loggerInfo(
      "Intelligent auto-optimization completed",
      storeName,
      {
        actionsPerformed: results.length,
        recommendations: recommendations.length,
        outOfStockHidden: results.filter(r => r.action === 'hide_out_of_stock').length,
        highInventoryPromoted: results.filter(r => r.action === 'promote_high_inventory').length,
        collectionsOptimized: results.filter(r => r.action === 'optimize_collection').length
      }
    );

    return results;

  } catch (error) {
    loggerError(
      "Error during intelligent auto-optimization",
      storeName,
      error.message
    );

    return [{
      status: 500,
      message: "Auto-optimization failed",
      data: null,
      error: error.message
    }];
  }
}

export const getInventorySettings = async (shopDomain) => {
  try {
    const { data: settings, error } = await supabase
      .from('inventorysettings')
      .select('*')
      .eq('shop_domain', shopDomain)
      .single();

    if (error && error.code !== 'PGRST116') { // PGRST116 is "not found" error
      throw error;
    }

    if (settings) {
      return {
        shopDomain: settings.shop_domain,
        autoHideOutOfStock: settings.auto_hide_out_of_stock,
        promoteHighInventory: settings.promote_high_inventory,
        lowInventoryThreshold: settings.low_inventory_threshold,
        highInventoryThreshold: settings.high_inventory_threshold,
        updateFrequency: settings.update_frequency
      };
    }

    // Return default settings
    return {
      shopDomain,
      autoHideOutOfStock: true,
      promoteHighInventory: true,
      lowInventoryThreshold: 10,
      highInventoryThreshold: 100,
      updateFrequency: 'daily'
    };
  } catch (error) {
    loggerError("Error getting inventory settings", shopDomain, error.message);

    return {
      shopDomain,
      autoHideOutOfStock: true,
      promoteHighInventory: true,
      lowInventoryThreshold: 10,
      highInventoryThreshold: 100,
      updateFrequency: 'daily'
    };
  }
};

export const updateInventorySettings = async (shopDomain, newSettings) => {
  try {
    const settingsData = {
      shop_domain: shopDomain,
      auto_hide_out_of_stock: newSettings.autoHideOutOfStock,
      promote_high_inventory: newSettings.promoteHighInventory,
      low_inventory_threshold: newSettings.lowInventoryThreshold,
      high_inventory_threshold: newSettings.highInventoryThreshold,
      update_frequency: newSettings.updateFrequency,
      updated_at: new Date().toISOString()
    };

    const { data: settings, error } = await supabase
      .from('inventorysettings')
      .upsert(settingsData, {
        onConflict: 'shop_domain',
        returning: 'representation'
      })
      .select()
      .single();

    if (error) {
      throw error;
    }

    loggerInfo("Inventory settings updated successfully", shopDomain);

    return {
      status: 200,
      message: "Settings updated successfully",
      data: {
        shopDomain: settings.shop_domain,
        autoHideOutOfStock: settings.auto_hide_out_of_stock,
        promoteHighInventory: settings.promote_high_inventory,
        lowInventoryThreshold: settings.low_inventory_threshold,
        highInventoryThreshold: settings.high_inventory_threshold,
        updateFrequency: settings.update_frequency
      }
    };
  } catch (error) {
    loggerError("Error updating inventory settings", shopDomain, error.message);

    return {
      status: 500,
      message: "Failed to update settings",
      data: null
    };
  }
};

export const getInventoryInsights = async (admin) => {
  const shop = await shopDetails(admin);
  const storeName = shop?.data?.shop?.name;

  try {
    const inventoryData = await getInventoryData(admin);

    if (inventoryData.status !== 200) {
      throw new Error("Failed to fetch inventory data");
    }

    const products = inventoryData.data;

    const insights = {
      totalProducts: products.length,
      inStock: products.filter(p => p.inventory > 10).length,
      lowStock: products.filter(p => p.inventory > 0 && p.inventory <= 10).length,
      outOfStock: products.filter(p => p.inventory === 0).length,
      averageInventory: Math.round(
        products.reduce((sum, p) => sum + p.inventory, 0) / products.length
      ),
      totalInventoryValue: products.reduce(
        (sum, p) => sum + (p.inventory * parseFloat(p.price)), 0
      ).toFixed(2),
      recommendations: generateInventoryRecommendations(products)
    };

    return {
      status: 200,
      message: "Inventory insights generated successfully",
      data: insights
    };

  } catch (error) {
    loggerError("Error generating inventory insights", storeName, error.message);

    return {
      status: 500,
      message: "Failed to generate insights",
      data: null
    };
  }
};

function generateInventoryRecommendations(products) {
  const recommendations = [];

  const outOfStockCount = products.filter(p => p.inventory === 0).length;
  const lowStockCount = products.filter(p => p.inventory > 0 && p.inventory <= 10).length;

  if (outOfStockCount > 0) {
    recommendations.push({
      type: 'critical',
      title: 'Hide Out-of-Stock Products',
      description: `${outOfStockCount} products are out of stock and should be hidden from collections`,
      action: 'hide_out_of_stock'
    });
  }

  if (lowStockCount > 0) {
    recommendations.push({
      type: 'warning',
      title: 'Restock Low Inventory Items',
      description: `${lowStockCount} products have low inventory and may need restocking`,
      action: 'restock_items'
    });
  }

  const highInventoryProducts = products.filter(p => p.inventory > 100);
  if (highInventoryProducts.length > 0) {
    recommendations.push({
      type: 'opportunity',
      title: 'Promote High-Inventory Products',
      description: `${highInventoryProducts.length} products have high inventory and could be promoted`,
      action: 'promote_high_inventory'
    });
  }

  return recommendations;
}

// New intelligent optimization functions
async function optimizeCollectionsByInventory(admin, products, settings) {
  const results = [];

  try {
    // Get all collections that contain these products
    const collectionsQuery = `
      query getCollections($first: Int!) {
        collections(first: $first) {
          edges {
            node {
              id
              title
              productsCount
              products(first: 250) {
                edges {
                  node {
                    id
                    title
                    totalInventory
                    variants(first: 10) {
                      edges {
                        node {
                          id
                          inventoryQuantity
                          price
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    `;

    const collectionsResponse = await admin.graphql(collectionsQuery, {
      variables: { first: 50 }
    });

    const collectionsData = await collectionsResponse.json();

    if (collectionsData.data?.collections?.edges) {
      for (const collectionEdge of collectionsData.data.collections.edges) {
        const collection = collectionEdge.node;

        // Skip collections with too few products
        if (collection.productsCount < 3) continue;

        const collectionProducts = collection.products.edges.map(edge => edge.node);

        // Calculate inventory scores for products in this collection
        const productsWithScores = collectionProducts.map(product => {
          const inventory = product.totalInventory || 0;
          const avgPrice = product.variants.edges.length > 0
            ? product.variants.edges.reduce((sum, v) => sum + parseFloat(v.node.price), 0) / product.variants.edges.length
            : 0;

          // Score based on inventory level and price
          let score = 0;
          if (inventory === 0) {
            score = -100; // Heavily penalize out of stock
          } else if (inventory < settings.lowInventoryThreshold) {
            score = inventory * 2; // Low inventory gets lower score
          } else if (inventory >= settings.highInventoryThreshold) {
            score = 100 + (avgPrice * 0.1); // High inventory + price consideration
          } else {
            score = inventory + (avgPrice * 0.05); // Normal scoring
          }

          return {
            ...product,
            inventoryScore: score,
            inventory,
            avgPrice
          };
        });

        // Sort by inventory score (highest first)
        const optimizedOrder = productsWithScores
          .sort((a, b) => b.inventoryScore - a.inventoryScore)
          .map(p => p.id);

        // Check if order actually changed
        const currentOrder = collectionProducts.map(p => p.id);
        const orderChanged = !optimizedOrder.every((id, index) => id === currentOrder[index]);

        if (orderChanged) {
          results.push({
            status: 200,
            action: 'optimize_collection',
            collectionTitle: collection.title,
            collectionId: collection.id,
            reason: 'Reordered products based on inventory levels and pricing',
            productsReordered: optimizedOrder.length,
            message: `Optimized product order in collection "${collection.title}"`
          });
        }
      }
    }
  } catch (error) {
    loggerError("Error optimizing collections by inventory", null, error.message);
    results.push({
      status: 500,
      action: 'optimize_collection',
      message: "Failed to optimize collections",
      error: error.message
    });
  }

  return results;
}

async function generateInventoryOptimizationRecommendations(products, settings) {
  const recommendations = [];

  // Analyze inventory distribution
  const totalProducts = products.length;
  const outOfStock = products.filter(p => p.inventory === 0);
  const lowStock = products.filter(p => p.inventory > 0 && p.inventory <= settings.lowInventoryThreshold);
  const highStock = products.filter(p => p.inventory >= settings.highInventoryThreshold);
  const normalStock = products.filter(p =>
    p.inventory > settings.lowInventoryThreshold &&
    p.inventory < settings.highInventoryThreshold
  );

  // Calculate inventory value
  const totalInventoryValue = products.reduce((sum, p) =>
    sum + (p.inventory * parseFloat(p.price || 0)), 0
  );

  // Generate specific recommendations
  if (outOfStock.length > totalProducts * 0.1) { // More than 10% out of stock
    recommendations.push({
      type: 'critical',
      title: 'High Out-of-Stock Rate',
      description: `${outOfStock.length} products (${((outOfStock.length / totalProducts) * 100).toFixed(1)}%) are out of stock`,
      action: 'restock_priority',
      priority: 'high',
      impact: 'revenue_loss'
    });
  }

  if (lowStock.length > totalProducts * 0.15) { // More than 15% low stock
    recommendations.push({
      type: 'warning',
      title: 'Many Products Running Low',
      description: `${lowStock.length} products need restocking soon`,
      action: 'restock_planning',
      priority: 'medium',
      impact: 'potential_stockout'
    });
  }

  if (highStock.length > totalProducts * 0.2) { // More than 20% high stock
    recommendations.push({
      type: 'opportunity',
      title: 'Excess Inventory Opportunity',
      description: `${highStock.length} products have high inventory - consider promotions`,
      action: 'promote_excess_inventory',
      priority: 'low',
      impact: 'cash_flow_improvement'
    });
  }

  // Inventory balance recommendation
  const inventoryBalance = normalStock.length / totalProducts;
  if (inventoryBalance < 0.5) {
    recommendations.push({
      type: 'strategic',
      title: 'Inventory Imbalance Detected',
      description: 'Consider rebalancing inventory levels across products',
      action: 'inventory_rebalancing',
      priority: 'medium',
      impact: 'operational_efficiency'
    });
  }

  return recommendations;
}
