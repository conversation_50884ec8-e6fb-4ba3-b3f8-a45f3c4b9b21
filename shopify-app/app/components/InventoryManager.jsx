import React, { useState, useCallback } from 'react';
import {
  Card,
  BlockStack,
  InlineStack,
  Box,
  Text,
  Button,
  Badge,
  Icon,
  Popover,
  Select,
  TextField,
  Checkbox,
  DataTable,
  Filters,
  ChoiceList,
  RangeSlider,
  Tooltip,
  Banner,
} from '@shopify/polaris';
import {
  HomeIcon,
  InfoIcon,
  CheckCircleIcon,
  SettingsIcon,
  WandIcon
} from '@shopify/polaris-icons';

const InventoryStatusBadge = ({ inventory, threshold }) => {
  if (inventory === 0) {
    return <Badge tone="critical">Out of Stock</Badge>;
  } else if (inventory <= threshold) {
    return <Badge tone="warning">Low Stock</Badge>;
  } else {
    return <Badge tone="success">In Stock</Badge>;
  }
};

// Helper function to get status text
const getInventoryStatusText = (inventory, threshold) => {
  if (inventory === 0) {
    return 'Out of Stock';
  } else if (inventory <= threshold) {
    return 'Low Stock';
  } else {
    return 'In Stock';
  }
};

const ProductCard = ({ product, onToggleVisibility, onUpdateInventory, isSelected, onSelect }) => {
  const [isUpdating, setIsUpdating] = useState(false);

  const handleToggle = async () => {
    setIsUpdating(true);
    await onToggleVisibility(product.id, !product.isVisible);
    setIsUpdating(false);
  };

  return (
    <Card>
      <Box padding="400">
        <InlineStack align="space-between" blockAlign="start">
          <InlineStack gap="300" blockAlign="center">
            <Checkbox
              checked={isSelected}
              onChange={onSelect}
            />
            <Box
              style={{
                width: '50px',
                height: '50px',
                backgroundColor: '#f6f6f7',
                borderRadius: '8px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              {product.image ? (
                <img
                  src={product.image}
                  alt={product.title}
                  style={{
                    width: '100%',
                    height: '100%',
                    objectFit: 'cover',
                    borderRadius: '8px',
                  }}
                />
              ) : (
                <Icon source={HomeIcon} tone="subdued" />
              )}
            </Box>
            <BlockStack gap="100">
              <Text variant="bodyMd" fontWeight="semibold">
                {product.title}
              </Text>
              <Text variant="bodySm" color="subdued">
                {product.collections?.join(', ') || 'No collections'}
              </Text>
              <InlineStack gap="200">
                <Text variant="bodySm">
                  <Text as="span" fontWeight="medium">Inventory:</Text> {product.inventory}
                </Text>
                <InventoryStatusBadge inventory={product.inventory} threshold={10} />
              </InlineStack>
            </BlockStack>
          </InlineStack>

          <InlineStack gap="200" blockAlign="center">
            <InlineStack gap="100" blockAlign="center">
              <Checkbox
                checked={product.isVisible}
                onChange={handleToggle}
                disabled={isUpdating}
              />
              <Text variant="bodySm" color="subdued">
                {product.isVisible ? 'Visible' : 'Hidden'}
              </Text>
            </InlineStack>
            <Button
              size="slim"
              onClick={() => onUpdateInventory(product.id)}
              disabled={isUpdating}
            >
              Update
            </Button>
          </InlineStack>
        </InlineStack>
      </Box>
    </Card>
  );
};

const InventorySettings = ({ isOpen, onClose, settings, onSave }) => {
  const [localSettings, setLocalSettings] = useState(settings);

  const handleSave = () => {
    onSave(localSettings);
    onClose();
  };

  if (!isOpen) return null;

  return (
    <Card>
      <Box padding="400">
        <BlockStack gap="400">
          <InlineStack align="space-between">
            <Text variant="headingMd">Inventory Management Settings</Text>
            <Button onClick={onClose}>Close</Button>
          </InlineStack>
          <Text variant="headingMd">Automatic Actions</Text>

          <Checkbox
            label="Automatically hide out-of-stock products"
            checked={localSettings.autoHideOutOfStock}
            onChange={(checked) => setLocalSettings(prev => ({
              ...prev,
              autoHideOutOfStock: checked
            }))}
            helpText="Products with 0 inventory will be automatically hidden from collections"
          />

          <Checkbox
            label="Promote high-inventory products"
            checked={localSettings.promoteHighInventory}
            onChange={(checked) => setLocalSettings(prev => ({
              ...prev,
              promoteHighInventory: checked
            }))}
            helpText="Products with high inventory will be moved to the top of collections"
          />

          <Box>
            <Text variant="bodyMd" fontWeight="medium">Low Inventory Threshold</Text>
            <Box paddingBlockStart="200">
              <RangeSlider
                label="Units"
                value={localSettings.lowInventoryThreshold}
                onChange={(value) => setLocalSettings(prev => ({
                  ...prev,
                  lowInventoryThreshold: value
                }))}
                min={1}
                max={100}
                step={1}
              />
            </Box>
            <Text variant="bodySm" color="subdued">
              Products with inventory below {localSettings.lowInventoryThreshold} units will be flagged as low stock
            </Text>
          </Box>

          <Box>
            <Text variant="bodyMd" fontWeight="medium">High Inventory Threshold</Text>
            <Box paddingBlockStart="200">
              <RangeSlider
                label="Units"
                value={localSettings.highInventoryThreshold}
                onChange={(value) => setLocalSettings(prev => ({
                  ...prev,
                  highInventoryThreshold: value
                }))}
                min={50}
                max={1000}
                step={10}
              />
            </Box>
            <Text variant="bodySm" color="subdued">
              Products with inventory above {localSettings.highInventoryThreshold} units will be promoted
            </Text>
          </Box>

          <Select
            label="Update Frequency"
            options={[
              { label: 'Real-time', value: 'realtime' },
              { label: 'Every hour', value: 'hourly' },
              { label: 'Daily', value: 'daily' },
              { label: 'Manual only', value: 'manual' },
            ]}
            value={localSettings.updateFrequency}
            onChange={(value) => setLocalSettings(prev => ({
              ...prev,
              updateFrequency: value
            }))}
          />

          <InlineStack gap="200">
            <Button variant="primary" onClick={handleSave}>
              Save Settings
            </Button>
            <Button onClick={onClose}>
              Cancel
            </Button>
          </InlineStack>
        </BlockStack>
      </Box>
    </Card>
  );
};

export default function InventoryManager({
  products = [],
  loading = false,
  onToggleProductVisibility,
  onUpdateProductInventory,
  onBulkAction,
  settings,
  onUpdateSettings
}) {
  const [isSettingsOpen, setIsSettingsOpen] = useState(false);
  const [selectedProducts, setSelectedProducts] = useState([]);
  const [filters, setFilters] = useState({
    inventoryStatus: [],
    collections: [],
    searchQuery: ''
  });

  // Filter products based on current filters
  const filteredProducts = products.filter(product => {
    if (filters.searchQuery && !product.title.toLowerCase().includes(filters.searchQuery.toLowerCase())) {
      return false;
    }

    if (filters.inventoryStatus.length > 0) {
      const status = product.inventory === 0 ? 'out-of-stock' :
        product.inventory <= 10 ? 'low-stock' : 'in-stock';
      if (!filters.inventoryStatus.includes(status)) {
        return false;
      }
    }

    return true;
  });

  const handleBulkHide = useCallback(() => {
    onBulkAction('hide', selectedProducts);
    setSelectedProducts([]);
  }, [selectedProducts, onBulkAction]);

  const handleBulkShow = useCallback(() => {
    onBulkAction('show', selectedProducts);
    setSelectedProducts([]);
  }, [selectedProducts, onBulkAction]);

  const handleAutoOptimize = useCallback(() => {
    console.log('Auto-optimize triggered from InventoryManager');
    onBulkAction('auto-optimize', []);
  }, [onBulkAction]);

  const outOfStockCount = products.filter(p => p.inventory === 0).length;
  const lowStockCount = products.filter(p => p.inventory > 0 && p.inventory <= 10).length;

  return (
    <>
      <Card>
        <Box padding="400">
          <BlockStack gap="400">
            <InlineStack align="space-between" blockAlign="center">
              <BlockStack gap="100">
                <Text variant="headingMd" fontWeight="semibold">
                  Inventory Management
                </Text>
                <InlineStack gap="200">
                  {outOfStockCount > 0 && (
                    <Badge tone="critical">{outOfStockCount} Out of Stock</Badge>
                  )}
                  {lowStockCount > 0 && (
                    <Badge tone="warning">{lowStockCount} Low Stock</Badge>
                  )}
                  <Text variant="bodySm" color="subdued">
                    {products.length} total products
                  </Text>
                </InlineStack>
              </BlockStack>

              <InlineStack gap="200">
                <Button
                  icon={WandIcon}
                  onClick={handleAutoOptimize}
                >
                  Auto-Optimize
                </Button>
                <Button
                  icon={SettingsIcon}
                  onClick={() => setIsSettingsOpen(true)}
                >
                  Settings
                </Button>
              </InlineStack>
            </InlineStack>

            {(outOfStockCount > 0 || lowStockCount > 0) && (
              <Banner
                title="Inventory Issues Detected"
                tone="warning"
                action={{
                  content: 'Auto-fix Issues',
                  onAction: handleAutoOptimize
                }}
              >
                <Text>
                  {outOfStockCount > 0 && `${outOfStockCount} products are out of stock. `}
                  {lowStockCount > 0 && `${lowStockCount} products have low inventory. `}
                  Consider hiding out-of-stock items and promoting high-inventory products.
                </Text>
              </Banner>
            )}

            <Filters
              queryValue={filters.searchQuery}
              filters={[
                {
                  key: 'inventoryStatus',
                  label: 'Inventory Status',
                  filter: (
                    <ChoiceList
                      title="Inventory Status"
                      titleHidden
                      choices={[
                        { label: 'In Stock', value: 'in-stock' },
                        { label: 'Low Stock', value: 'low-stock' },
                        { label: 'Out of Stock', value: 'out-of-stock' },
                      ]}
                      selected={filters.inventoryStatus}
                      onChange={(value) => setFilters(prev => ({
                        ...prev,
                        inventoryStatus: value
                      }))}
                      allowMultiple
                    />
                  ),
                  shortcut: true,
                },
              ]}
              appliedFilters={[]}
              onQueryChange={(value) => setFilters(prev => ({
                ...prev,
                searchQuery: value
              }))}
              onQueryClear={() => setFilters(prev => ({
                ...prev,
                searchQuery: ''
              }))}
              onClearAll={() => setFilters({
                inventoryStatus: [],
                collections: [],
                searchQuery: ''
              })}
            />

            {selectedProducts.length > 0 && (
              <InlineStack gap="200">
                <Text variant="bodyMd">
                  {selectedProducts.length} products selected
                </Text>
                <Button size="slim" onClick={handleBulkHide}>
                  Hide Selected
                </Button>
                <Button size="slim" onClick={handleBulkShow}>
                  Show Selected
                </Button>
              </InlineStack>
            )}

            {/* Product List with Custom Layout instead of DataTable */}
            <BlockStack gap="300">
              {filteredProducts.length === 0 ? (
                <Card>
                  <Box padding="400">
                    <BlockStack gap="300" align="center">
                      <Text variant="headingMd">No Products Found</Text>
                      <Text variant="bodyMd" color="subdued" alignment="center">
                        {products.length === 0
                          ? "No products available for inventory management."
                          : "No products match your current filters."
                        }
                      </Text>
                    </BlockStack>
                  </Box>
                </Card>
              ) : (
                filteredProducts.map(product => (
                  <ProductCard
                    key={product.id}
                    product={product}
                    onToggleVisibility={onToggleProductVisibility}
                    onUpdateInventory={onUpdateProductInventory}
                    isSelected={selectedProducts.includes(product.id)}
                    onSelect={(selected) => {
                      if (selected) {
                        setSelectedProducts(prev => [...prev, product.id]);
                      } else {
                        setSelectedProducts(prev => prev.filter(id => id !== product.id));
                      }
                    }}
                  />
                ))
              )}
            </BlockStack>
          </BlockStack>
        </Box>
      </Card>

      <InventorySettings
        isOpen={isSettingsOpen}
        onClose={() => setIsSettingsOpen(false)}
        settings={settings}
        onSave={onUpdateSettings}
      />
    </>
  );
}
