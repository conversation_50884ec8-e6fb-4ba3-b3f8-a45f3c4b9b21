/**
 * Simple in-memory cache for performance optimization
 * In production, consider using Redis or another persistent cache
 */

class MemoryCache {
  constructor() {
    this.cache = new Map();
    this.ttl = new Map(); // Time to live for each key
  }

  /**
   * Set a value in cache with optional TTL
   * @param {string} key - Cache key
   * @param {any} value - Value to cache
   * @param {number} ttlMs - Time to live in milliseconds (default: 5 minutes)
   */
  set(key, value, ttlMs = 5 * 60 * 1000) {
    this.cache.set(key, value);
    this.ttl.set(key, Date.now() + ttlMs);
  }

  /**
   * Get a value from cache
   * @param {string} key - Cache key
   * @returns {any|null} - Cached value or null if not found/expired
   */
  get(key) {
    const expiry = this.ttl.get(key);
    
    // Check if key exists and hasn't expired
    if (!expiry || Date.now() > expiry) {
      this.delete(key);
      return null;
    }

    return this.cache.get(key);
  }

  /**
   * Delete a key from cache
   * @param {string} key - Cache key
   */
  delete(key) {
    this.cache.delete(key);
    this.ttl.delete(key);
  }

  /**
   * Clear all cache entries
   */
  clear() {
    this.cache.clear();
    this.ttl.clear();
  }

  /**
   * Get cache statistics
   * @returns {object} - Cache stats
   */
  getStats() {
    return {
      size: this.cache.size,
      keys: Array.from(this.cache.keys())
    };
  }

  /**
   * Clean up expired entries
   */
  cleanup() {
    const now = Date.now();
    for (const [key, expiry] of this.ttl.entries()) {
      if (now > expiry) {
        this.delete(key);
      }
    }
  }
}

// Create a global cache instance
const cache = new MemoryCache();

// Clean up expired entries every 10 minutes
setInterval(() => {
  cache.cleanup();
}, 10 * 60 * 1000);

export default cache;

/**
 * Generate cache key for collection analytics
 * @param {string} shopDomain - Shop domain
 * @param {string} collectionId - Collection ID
 * @param {string} timeframe - Timeframe for analytics
 * @returns {string} - Cache key
 */
export const getCollectionAnalyticsCacheKey = (shopDomain, collectionId, timeframe) => {
  return `analytics:${shopDomain}:${collectionId}:${timeframe}`;
};

/**
 * Generate cache key for inventory alerts
 * @param {string} shopDomain - Shop domain
 * @param {number} threshold - Inventory threshold
 * @returns {string} - Cache key
 */
export const getInventoryAlertsCacheKey = (shopDomain, threshold) => {
  return `inventory:${shopDomain}:${threshold}`;
};

/**
 * Generate cache key for performance alerts
 * @param {string} shopDomain - Shop domain
 * @returns {string} - Cache key
 */
export const getPerformanceAlertsCacheKey = (shopDomain) => {
  return `alerts:${shopDomain}`;
};
